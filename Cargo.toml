[workspace]
resolver = "2"
members = [
    "example",
    "polyhal-macro",
    "polyhal",
    "polyhal-boot",
    "polyhal-trap",
]

[workspace.package]
version = "0.4.0"
edition = "2021"
description = "This crate provides the interface for multiple platforms."
authors = ["yfblock@<EMAIL>"]
license = "MIT"
repository = "https://github.com/Byte-OS/polyhal"

[workspace.dependencies]
polyhal = { version = "0.4.0" }
polyhal-boot = { version = "0.4.0" }
polyhal-macro = { version = "0.4.0" }
polyhal-trap = { version = "0.4.0" }

bitflags = "2.9.0"
cfg-if = "1.0.0"

[patch.crates-io]
polyhal = { path = "polyhal" }
polyhal-boot = { path = "polyhal-boot" }
polyhal-macro = { path = "polyhal-macro" }
polyhal-trap = { path = "polyhal-trap" }

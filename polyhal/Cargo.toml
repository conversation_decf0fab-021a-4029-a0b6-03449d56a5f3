[package]
name = "polyhal"
version = { workspace = true }
edition = { workspace = true }
description = { workspace = true }
authors = { workspace = true }
license = { workspace = true }
repository = { workspace = true }

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[features]
trap = []
boot = []
logger = []
fp_simd = []

graphic = []

default = []

[dependencies]
log = "0.4"
fdt-parser = "0.4.10"
bitflags = { workspace = true }
cfg-if = { workspace = true }
polyhal-macro = { workspace = true }
spin = { version = "0.10.0", features = ["mutex"] }

lazyinit = "0.2.1"
arrayvec = { version = "0.7.6", default-features = false }

[target.'cfg(target_arch = "riscv64")'.dependencies]
riscv = "0.13.0"
sbi-rt = { version = "0.0.2", features = ["legacy"] }

[target.'cfg(target_arch = "x86_64")'.dependencies]
x86 = "0.52"
x86_64 = "=0.15.2"
multiboot = "0.8.0"
x2apic = "0.5"
raw-cpuid = "11.0"
acpi = { version = "5.2.0", default-features = false }
uart_16550 = "0.3.2"

[target.'cfg(target_arch = "aarch64")'.dependencies]
aarch64-cpu = "9.3"
arm_pl011 = "0.1.0"
tock-registers = "0.8"
arm_gicv2 = "0.1.0"

[target.'cfg(target_arch = "loongarch64")'.dependencies]
loongArch64 = "0.2.4"
ns16550a = "0.5.0"

[build-dependencies]
autocfg = "1.4.0"

[package]
name = "example"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
virtio-drivers = "0.7.5"
polyhal = { workspace = true, features = ["logger", "trap"] }
polyhal-boot = { workspace = true }
polyhal-trap = { workspace = true }
log = "0.4"
buddy_system_allocator = "0.11.0"
spin = { version = "0.10.0", features = ["mutex"] }

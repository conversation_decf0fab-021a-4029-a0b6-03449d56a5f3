[package]
name = "polyhal-macro"
version = { workspace = true }
edition = { workspace = true }
description = { workspace = true }
authors = { workspace = true }
license = { workspace = true }
repository = { workspace = true }

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
proc-macro = true

[dependencies]
proc-macro2 = { version = "1", features = ["nightly"] }
quote = "1"
syn = { version = "2.0", features = ["full"] }
proc-macro-crate = "3.3.0"
